using System;
using System.Collections;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Parameters container for the SetPostProcessing command configuration.
    /// </summary>
    [Serializable]
    public class SetPostProcessingParams
    {
        /// <summary>
        /// The post-processing profile to set.
        /// </summary>
        public string profileAddress;

        /// <summary>
        /// The intensity/weight of the post-processing effect (0.0 to 1.0).
        /// </summary>
        [Range(0f, 1f)]
        public float intensity = 1.0f;
    }

    /// <summary>
    /// Command to set post-processing profile.
    /// </summary>
    public class SetPostProcessingCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the set post-processing command.</summary>
        public SetPostProcessingParams Parameters { get; }

        /// <summary>Creates a new SetPostProcessingCommand.</summary>
        /// <param name="parameters">Set post-processing parameters.</param>
        public SetPostProcessingCommand(SetPostProcessingParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            yield break;
        }
    }
}
