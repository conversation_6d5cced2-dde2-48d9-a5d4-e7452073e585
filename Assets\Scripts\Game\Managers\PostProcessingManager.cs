using System.Collections.Generic;
using Game.CommandSystem;
using SmartVertex.Tools;
using UnityEngine;
using UnityEngine.Rendering.PostProcessing;

namespace Game.Managers
{
    public class PostProcessingManager : Singleton<PostProcessingManager>
    {
        [SerializeField] private PostProcessVolume globalVolume;
        [SerializeField] private PostProcessProfile defaultProfile;

        private Dictionary<string, PostProcessProfile> profileCache = new();
        private AddressablesHelper addressablesHelper = new();

        /// <summary>
        /// Generates and caches profiles for the provided commands.
        /// </summary>
        /// <param name="commands">List of coroutine commands.</param>
        public async Awaitable GenerateCache(List<ICoroutineCommand> commands)
        {
            foreach (var command in commands)
            {
                if (command is SetPostProcessingCommand setPostProcessing)
                {
                    profileCache[setPostProcessing.Parameters.profileAddress] = await addressablesHelper.LoadAssetAsync<PostProcessProfile>(setPostProcessing.Parameters.profileAddress);
                }
                else if (command is CommandSystem.CompositeCommand compositeCommand)
                {
                    await GenerateCache(compositeCommand.Commands);
                }
            }
        }

        public void SetProfile(string profilesAddress, float weight)
        {
            var profile = profileCache[profilesAddress];
            globalVolume.profile = profile;
            globalVolume.weight = weight;
        }
    }
}